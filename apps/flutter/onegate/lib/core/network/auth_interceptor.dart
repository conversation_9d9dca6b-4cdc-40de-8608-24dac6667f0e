import 'dart:developer';
import 'package:dio/dio.dart';
import 'package:flutter_onegate/services/auth_service/auth_service.dart';

/// Unified authentication interceptor that automatically injects tokens
/// and handles token refresh for all API requests
class UnifiedAuthInterceptor extends Interceptor {
  final AuthService _authService;
  final Duration _retryDelay = const Duration(milliseconds: 500);
  
  // Endpoints that should skip authentication
  final Set<String> _skipAuthEndpoints = {
    '/gatelogin',
    '/auth',
    '/login',
    '/register',
    '/forgot-password',
    '/reset-password',
    'gate_base_domain',
    'gate_facial',
  };
  
  UnifiedAuthInterceptor({
    required AuthService authService,
  }) : _authService = authService;
  
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    try {
      // Skip auth for certain endpoints
      if (_shouldSkipAuth(options.path)) {
        log('⏭️ Skipping auth for endpoint: ${options.path}');
        handler.next(options);
        return;
      }
      
      // Get current access token
      final accessToken = await _authService.getValidAccessToken();
      
      if (accessToken != null && accessToken.isNotEmpty) {
        options.headers['Authorization'] = 'Bearer $accessToken';
        log('🔑 Added Bearer token to request: ${options.path}');
        
        // Log token expiration info in debug mode
        if (options.extra['debug_token'] == true) {
          _logTokenInfo(accessToken);
        }
      } else {
        log('⚠️ No access token available for request: ${options.path}');
        // Don't fail the request here, let the server respond with 401
      }
      
      handler.next(options);
    } catch (e) {
      log('❌ Error in auth interceptor onRequest: $e');
      handler.next(options);
    }
  }
  
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    // Handle 401 Unauthorized errors with automatic retry
    if (err.response?.statusCode == 401) {
      log('🔄 Received 401 Unauthorized for: ${err.requestOptions.path}');
      
      final retryResult = await _handleUnauthorizedError(err);
      if (retryResult != null) {
        handler.resolve(retryResult);
        return;
      }
    }
    
    // Handle other authentication-related errors
    if (_isAuthenticationError(err)) {
      log('🚫 Authentication error detected: ${err.response?.statusCode}');
      await _handleAuthenticationFailure();
    }
    
    handler.next(err);
  }
  
  /// Check if the endpoint should skip authentication
  bool _shouldSkipAuth(String path) {
    return _skipAuthEndpoints.any((endpoint) => 
        path.toLowerCase().contains(endpoint.toLowerCase()));
  }
  
  /// Handle 401 Unauthorized errors with token refresh and retry
  Future<Response?> _handleUnauthorizedError(DioException error) async {
    try {
      // Check retry count to prevent infinite loops
      final retryCount = error.requestOptions.extra['retry_count'] ?? 0;
      if (retryCount >= 1) {
        log('🚫 Max retry attempts reached for: ${error.requestOptions.path}');
        return null;
      }
      
      log('🔄 Attempting token refresh for 401 error...');
      
      // Attempt to refresh the token
      final refreshSuccess = await _authService.refreshToken();
      if (!refreshSuccess) {
        log('❌ Token refresh failed, cannot retry request');
        return null;
      }
      
      // Get the new access token
      final newAccessToken = await _authService.getValidAccessToken();
      if (newAccessToken == null) {
        log('❌ No new access token available after refresh');
        return null;
      }
      
      log('✅ Token refreshed successfully, retrying request');
      
      // Update request with new token and retry count
      final requestOptions = error.requestOptions;
      requestOptions.headers['Authorization'] = 'Bearer $newAccessToken';
      requestOptions.extra['retry_count'] = retryCount + 1;
      
      log('🔄 Retrying request with new token: ${requestOptions.path}');
      
      // Add delay before retry
      await Future.delayed(_retryDelay);
      
      // Create new Dio instance to avoid interceptor loops
      final dio = Dio();
      dio.options.baseUrl = requestOptions.baseUrl;
      dio.options.connectTimeout = requestOptions.connectTimeout;
      dio.options.receiveTimeout = requestOptions.receiveTimeout;
      dio.options.sendTimeout = requestOptions.sendTimeout;
      
      // Retry the request
      return await dio.request(
        requestOptions.path,
        data: requestOptions.data,
        queryParameters: requestOptions.queryParameters,
        options: Options(
          method: requestOptions.method,
          headers: requestOptions.headers,
          contentType: requestOptions.contentType,
          responseType: requestOptions.responseType,
          extra: requestOptions.extra,
        ),
      );
    } catch (e) {
      log('❌ Error handling 401 unauthorized: $e');
      return null;
    }
  }
  
  /// Check if the error is authentication-related
  bool _isAuthenticationError(DioException error) {
    final statusCode = error.response?.statusCode;
    return statusCode == 401 || statusCode == 403;
  }
  
  /// Handle authentication failure (logout user)
  Future<void> _handleAuthenticationFailure() async {
    try {
      log('🚪 Handling authentication failure - logging out user');
      await _authService.logout();
      // Here you could also emit an event or navigate to login screen
      // This depends on your app's navigation structure
    } catch (e) {
      log('❌ Error handling authentication failure: $e');
    }
  }
  
  /// Log token information for debugging
  void _logTokenInfo(String token) {
    try {
      // Basic token info without exposing sensitive data
      final parts = token.split('.');
      if (parts.length == 3) {
        log('🔍 Token structure: Valid JWT with ${parts[0].length}.${parts[1].length}.${parts[2].length} characters');
      } else {
        log('🔍 Token structure: Non-JWT token with ${token.length} characters');
      }
    } catch (e) {
      log('❌ Error logging token info: $e');
    }
  }
}

/// Extension for request options to add auth-related metadata
extension AuthRequestOptions on RequestOptions {
  /// Mark request to debug token information
  void enableTokenDebug() {
    extra['debug_token'] = true;
  }
  
  /// Get retry count for this request
  int get retryCount => extra['retry_count'] ?? 0;
  
  /// Set retry count for this request
  set retryCount(int count) => extra['retry_count'] = count;
  
  /// Check if this request should skip authentication
  bool get skipAuth => extra['skip_auth'] == true;
  
  /// Mark request to skip authentication
  void setSkipAuth() {
    extra['skip_auth'] = true;
  }
}

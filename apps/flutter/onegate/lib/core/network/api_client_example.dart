import 'dart:developer';
import 'package:dio/dio.dart';
import 'package:flutter_onegate/core/network/dio_client.dart';
import 'package:get_it/get_it.dart';

/// Example demonstrating automatic token injection in API calls
/// This shows how all API requests now automatically include authentication headers
class ApiClientExample {
  
  /// Example 1: Simple GET request with automatic token injection
  /// No need to manually add Authorization headers
  static Future<void> exampleGetRequest() async {
    try {
      // Get the centralized Dio instance with automatic token injection
      final dio = GetIt.I<Dio>();
      
      // Make a simple GET request - token is automatically injected
      final response = await dio.get('/api/gates');
      
      log('✅ GET request successful: ${response.statusCode}');
      log('📦 Response data: ${response.data}');
    } catch (e) {
      log('❌ GET request failed: $e');
    }
  }
  
  /// Example 2: POST request with automatic token injection
  /// Token is automatically added to all requests
  static Future<void> examplePostRequest() async {
    try {
      final dio = GetIt.I<Dio>();
      
      // Make a POST request - token is automatically injected
      final response = await dio.post(
        '/api/visitor/entry',
        data: {
          'name': '<PERSON>',
          'purpose': 'Meeting',
          'company_id': 123,
        },
      );
      
      log('✅ POST request successful: ${response.statusCode}');
      log('📦 Response data: ${response.data}');
    } catch (e) {
      log('❌ POST request failed: $e');
    }
  }
  
  /// Example 3: Request with custom options
  /// Token injection works with all Dio options
  static Future<void> exampleRequestWithOptions() async {
    try {
      final dio = GetIt.I<Dio>();
      
      // Make request with custom options - token still automatically injected
      final response = await dio.get(
        '/api/visitor/log',
        queryParameters: {'page': 1, 'limit': 10},
        options: Options(
          headers: {
            'Custom-Header': 'Custom-Value',
            // No need to add Authorization header - it's automatic!
          },
          receiveTimeout: const Duration(seconds: 10),
        ),
      );
      
      log('✅ Custom request successful: ${response.statusCode}');
    } catch (e) {
      log('❌ Custom request failed: $e');
    }
  }
  
  /// Example 4: Request that skips authentication
  /// For endpoints that don't require authentication
  static Future<void> exampleSkipAuthRequest() async {
    try {
      final dio = GetIt.I<Dio>();
      
      // Make request that skips authentication
      final response = await dio.get(
        '/api/public/config',
        options: Options(
          extra: {'skip_auth': true}, // This skips token injection
        ),
      );
      
      log('✅ Public request successful: ${response.statusCode}');
    } catch (e) {
      log('❌ Public request failed: $e');
    }
  }
  
  /// Example 5: Request with token debugging
  /// Enable detailed token logging for debugging
  static Future<void> exampleDebugTokenRequest() async {
    try {
      final dio = GetIt.I<Dio>();
      
      // Make request with token debugging enabled
      final response = await dio.get(
        '/api/gates',
        options: Options(
          extra: {'debug_token': true}, // This enables token debugging
        ),
      );
      
      log('✅ Debug request successful: ${response.statusCode}');
    } catch (e) {
      log('❌ Debug request failed: $e');
    }
  }
  
  /// Example 6: Handling 401 errors automatically
  /// The interceptor automatically refreshes tokens and retries
  static Future<void> exampleAutoRetryOn401() async {
    try {
      final dio = GetIt.I<Dio>();
      
      // This request will automatically handle 401 errors:
      // 1. If 401 is received, token is automatically refreshed
      // 2. Request is retried with new token
      // 3. If refresh fails, user is logged out
      final response = await dio.get('/api/protected-endpoint');
      
      log('✅ Protected request successful: ${response.statusCode}');
    } catch (e) {
      log('❌ Protected request failed: $e');
    }
  }
  
  /// Example 7: Using the DioClient directly
  /// Alternative way to get the configured Dio instance
  static Future<void> exampleUsingDioClient() async {
    try {
      // Get DioClient and ensure it's initialized
      final dioClient = GetIt.I<DioClient>();
      await dioClient.initialize();
      
      // Get the configured Dio instance
      final dio = dioClient.dio;
      
      // Make request - all features are available
      final response = await dio.get('/api/gates');
      
      log('✅ DioClient request successful: ${response.statusCode}');
    } catch (e) {
      log('❌ DioClient request failed: $e');
    }
  }
  
  /// Example 8: Batch requests with automatic token injection
  /// All requests in a batch automatically get tokens
  static Future<void> exampleBatchRequests() async {
    try {
      final dio = GetIt.I<Dio>();
      
      // Make multiple requests - all automatically authenticated
      final futures = [
        dio.get('/api/gates'),
        dio.get('/api/visitor/log'),
        dio.get('/api/buildings'),
      ];
      
      final responses = await Future.wait(futures);
      
      for (int i = 0; i < responses.length; i++) {
        log('✅ Batch request ${i + 1} successful: ${responses[i].statusCode}');
      }
    } catch (e) {
      log('❌ Batch requests failed: $e');
    }
  }
}

/// Usage instructions:
/// 
/// 1. All API requests now automatically include the current access token
/// 2. No need to manually add Authorization headers
/// 3. Token refresh is handled automatically on 401 errors
/// 4. Requests are automatically retried after token refresh
/// 5. Use 'skip_auth': true in options.extra to skip authentication
/// 6. Use 'debug_token': true in options.extra to enable token debugging
/// 
/// Example usage in your code:
/// ```dart
/// final dio = GetIt.I<Dio>();
/// final response = await dio.get('/api/your-endpoint');
/// // Token is automatically injected!
/// ```

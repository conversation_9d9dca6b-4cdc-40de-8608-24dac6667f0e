import 'dart:developer';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_onegate/core/network/auth_interceptor.dart';
import 'package:flutter_onegate/services/auth_service/auth_service.dart';
import 'package:flutter_onegate/utils/network_log/network_log_manager.dart';
import 'package:get_it/get_it.dart';

/// Centralized Dio client configuration with automatic token injection
class DioClient {
  static final DioClient _instance = DioClient._internal();
  
  factory DioClient() => _instance;
  
  DioClient._internal();
  
  late final Dio _dio;
  bool _isInitialized = false;
  
  /// Initialize the Dio client with all necessary interceptors
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    _dio = Dio();
    
    // Configure base options
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 30);
    _dio.options.sendTimeout = const Duration(seconds: 30);
    _dio.options.headers['Content-Type'] = 'application/json';
    
    // Add interceptors in order of priority
    await _addInterceptors();
    
    _isInitialized = true;
    log('✅ DioClient initialized with automatic token injection');
  }
  
  /// Add all necessary interceptors
  Future<void> _addInterceptors() async {
    // 1. Authentication interceptor (highest priority)
    _dio.interceptors.add(
      UnifiedAuthInterceptor(
        authService: GetIt.I<AuthService>(),
      ),
    );
    
    // 2. Network logging interceptor (debug mode only)
    if (kDebugMode) {
      NetworkLogManager().addInterceptorToDio(_dio);
    }
    
    // 3. General logging interceptor (debug mode only)
    if (kDebugMode) {
      _dio.interceptors.add(
        LogInterceptor(
          requestBody: true,
          responseBody: true,
          requestHeader: true,
          responseHeader: false,
          error: true,
          logPrint: (object) => log('🌐 HTTP: $object'),
        ),
      );
    }
    
    // 4. Error handling interceptor
    _dio.interceptors.add(_createErrorInterceptor());
  }
  
  /// Create error handling interceptor
  Interceptor _createErrorInterceptor() {
    return InterceptorsWrapper(
      onError: (error, handler) {
        log('❌ HTTP Error: ${error.response?.statusCode} - ${error.message}');
        
        // Handle specific error codes
        switch (error.response?.statusCode) {
          case 400:
            log('⚠️ Bad Request: ${error.response?.data}');
            break;
          case 403:
            log('🚫 Forbidden: Access denied');
            break;
          case 404:
            log('🔍 Not Found: ${error.requestOptions.path}');
            break;
          case 500:
            log('💥 Server Error: Internal server error');
            break;
          case 502:
            log('🌐 Bad Gateway: Server is down');
            break;
          case 503:
            log('⏰ Service Unavailable: Server temporarily unavailable');
            break;
        }
        
        handler.next(error);
      },
    );
  }
  
  /// Get the configured Dio instance
  Dio get dio {
    if (!_isInitialized) {
      throw StateError('DioClient not initialized. Call initialize() first.');
    }
    return _dio;
  }
  
  /// Convenience method to ensure initialization
  Future<Dio> getDio() async {
    await initialize();
    return _dio;
  }
}

# OneGate Automatic Token Injection Implementation

## 🎯 Overview

This document describes the implementation of automatic token injection for all API requests in the OneGate Flutter app. The system ensures that every HTTP request automatically includes the current valid access token without manual intervention.

## 📋 Implementation Summary

### ✅ **Completed Features**

1. **Centralized Dio Configuration** - Single, properly configured Dio instance
2. **Automatic Token Injection** - All requests automatically include Bearer tokens
3. **Token Management Integration** - Seamless integration with existing auth system
4. **Dynamic Token Updates** - Automatic token refresh and retry on 401 errors
5. **Clean Architecture Compliance** - Follows hexagonal architecture patterns
6. **Error Handling** - Graceful handling of authentication failures

## 🏗️ **Architecture Overview**

### **Core Components**

1. **DioClient** (`lib/core/network/dio_client.dart`)
   - Centralized Dio instance configuration
   - Automatic interceptor setup
   - Singleton pattern for consistency

2. **UnifiedAuthInterceptor** (`lib/core/network/auth_interceptor.dart`)
   - Automatic token injection for all requests
   - 401 error handling with token refresh and retry
   - Configurable endpoint exclusions

3. **Updated Dependency Injection** (`lib/presentation/di/di.dart`)
   - Registers DioClient as singleton
   - Provides configured Dio instance to all services

## 🔧 **Key Features**

### **1. Automatic Header Injection**
```dart
// Before: Manual token addition required
final response = await dio.get(
  '/api/gates',
  options: Options(
    headers: {
      'Authorization': 'Bearer $token', // Manual!
      'Content-Type': 'application/json',
    },
  ),
);

// After: Automatic token injection
final dio = GetIt.I<Dio>();
final response = await dio.get('/api/gates');
// Token automatically injected! 🎉
```

### **2. Token Management Integration**
- Integrates with existing `AuthService`
- Uses `getValidAccessToken()` for current token
- Automatic token refresh when needed

### **3. Dynamic Token Updates**
- Monitors token expiration
- Automatic refresh on 401 responses
- Seamless retry with new token
- No user interruption

### **4. Clean Architecture Compliance**
```
┌─────────────────────────────────────────┐
│              Presentation               │
│         (UI Components, BLoCs)          │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│               Domain                    │
│        (Use Cases, Entities)            │
└─────────────────┬───────────────────────┘
                  │
┌─────────────────▼───────────────────────┐
│                Data                     │
│    (Repositories, Data Sources)         │
│                 │                       │
│    ┌────────────▼────────────┐          │
│    │     DioClient           │          │
│    │  (Automatic Tokens)     │          │
│    └─────────────────────────┘          │
└─────────────────────────────────────────┘
```

### **5. Error Handling**
- Automatic 401 error detection
- Token refresh and request retry
- Graceful logout on refresh failure
- Comprehensive error logging

## 🚀 **Usage Examples**

### **Basic API Call**
```dart
// Get the configured Dio instance
final dio = GetIt.I<Dio>();

// Make any API call - token automatically included
final response = await dio.get('/api/gates');
final postResponse = await dio.post('/api/visitor/entry', data: {...});
```

### **Skip Authentication**
```dart
// For public endpoints that don't need authentication
final response = await dio.get(
  '/api/public/config',
  options: Options(
    extra: {'skip_auth': true},
  ),
);
```

### **Debug Token Information**
```dart
// Enable detailed token logging for debugging
final response = await dio.get(
  '/api/gates',
  options: Options(
    extra: {'debug_token': true},
  ),
);
```

## 🔄 **Token Refresh Flow**

```mermaid
sequenceDiagram
    participant App as App
    participant Interceptor as Auth Interceptor
    participant AuthService as Auth Service
    participant Server as API Server

    App->>+Interceptor: HTTP Request
    Interceptor->>Interceptor: Add Bearer Token
    Interceptor->>+Server: Request with Token
    Server-->>-Interceptor: 401 Unauthorized
    Interceptor->>+AuthService: Refresh Token
    AuthService-->>-Interceptor: New Access Token
    Interceptor->>Interceptor: Update Request Headers
    Interceptor->>+Server: Retry with New Token
    Server-->>-Interceptor: 200 Success
    Interceptor-->>-App: Response
```

## 📁 **File Structure**

```
lib/
├── core/
│   └── network/
│       ├── dio_client.dart              # Centralized Dio configuration
│       ├── auth_interceptor.dart        # Automatic token injection
│       └── api_client_example.dart      # Usage examples
├── presentation/
│   └── di/
│       └── di.dart                      # Updated dependency injection
└── main.dart                            # DioClient initialization
```

## ⚙️ **Configuration**

### **Endpoints that Skip Authentication**
The following endpoints automatically skip token injection:
- `/gatelogin`
- `/auth`
- `/login`
- `/register`
- `/forgot-password`
- `/reset-password`
- `gate_base_domain`
- `gate_facial`

### **Interceptor Order**
1. **Authentication Interceptor** (highest priority)
2. **Network Logging Interceptor** (debug mode only)
3. **General Logging Interceptor** (debug mode only)
4. **Error Handling Interceptor**

## 🧪 **Testing**

### **Manual Testing**
1. Make any API call using `GetIt.I<Dio>()`
2. Verify Authorization header is automatically added
3. Test 401 error handling by using expired token
4. Verify automatic retry after token refresh

### **Debug Features**
- Set `debug_token: true` in request options for detailed logging
- Monitor logs for token injection confirmation
- Check automatic retry behavior on 401 errors

## 🔒 **Security Features**

1. **Secure Token Storage** - Uses existing secure storage system
2. **Automatic Token Refresh** - Prevents expired token usage
3. **Request Retry Logic** - Handles temporary authentication failures
4. **Endpoint Exclusions** - Public endpoints skip authentication
5. **Error Logging** - Comprehensive logging without exposing sensitive data

## 🎉 **Benefits**

1. **Developer Experience** - No manual token management required
2. **Consistency** - All API calls use the same authentication pattern
3. **Reliability** - Automatic token refresh prevents authentication failures
4. **Maintainability** - Centralized configuration reduces code duplication
5. **Security** - Proper token handling and automatic refresh

## 🔄 **Migration Guide**

### **For Existing Code**
1. Replace manual Dio instances with `GetIt.I<Dio>()`
2. Remove manual Authorization header additions
3. Remove manual token refresh logic
4. Update error handling to rely on automatic retry

### **Example Migration**
```dart
// Before
final dio = Dio();
final token = await authService.getAccessToken();
final response = await dio.get(
  '/api/gates',
  options: Options(
    headers: {'Authorization': 'Bearer $token'},
  ),
);

// After
final dio = GetIt.I<Dio>();
final response = await dio.get('/api/gates');
```

## 📝 **Notes**

- The system is backward compatible with existing authentication flows
- Network logging is automatically integrated in debug mode
- All existing API services can gradually migrate to use the centralized client
- The implementation follows OneGate's established clean architecture patterns

## 🎯 **Next Steps**

1. **Gradual Migration** - Update existing API services to use centralized Dio
2. **Testing** - Comprehensive testing of token refresh scenarios
3. **Monitoring** - Add metrics for token refresh success rates
4. **Documentation** - Update API service documentation with new patterns

import 'package:flutter_test/flutter_test.dart';
import 'package:dio/dio.dart';
import 'package:flutter_onegate/core/network/dio_client.dart';

void main() {
  group('DioClient Tests', () {
    late DioClient dioClient;
    late MockAuthService mockAuthService;

    setUp(() {
      // Reset GetIt
      GetIt.instance.reset();

      // Create mocks
      mockAuthService = MockAuthService();

      // Register mocks in GetIt
      GetIt.instance.registerSingleton<AuthService>(mockAuthService);

      // Create DioClient instance
      dioClient = DioClient();
    });

    tearDown(() {
      GetIt.instance.reset();
    });

    test('should initialize DioClient successfully', () async {
      // Arrange
      when(mockAuthService.getValidAccessToken())
          .thenAnswer((_) async => 'test_token');

      // Act
      await dioClient.initialize();

      // Assert
      expect(dioClient.dio, isA<Dio>());
      expect(dioClient.dio.options.connectTimeout, const Duration(seconds: 30));
      expect(dioClient.dio.options.receiveTimeout, const Duration(seconds: 30));
      expect(dioClient.dio.options.sendTimeout, const Duration(seconds: 30));
    });

    test('should configure Dio with correct base options', () async {
      // Act
      await dioClient.initialize();

      // Assert
      final dio = dioClient.dio;
      expect(dio.options.headers['Content-Type'], 'application/json');
      expect(dio.options.connectTimeout, const Duration(seconds: 30));
      expect(dio.options.receiveTimeout, const Duration(seconds: 30));
      expect(dio.options.sendTimeout, const Duration(seconds: 30));
    });

    test('should add interceptors in correct order', () async {
      // Act
      await dioClient.initialize();

      // Assert
      final dio = dioClient.dio;
      expect(dio.interceptors.length, greaterThan(0));

      // First interceptor should be the auth interceptor
      expect(dio.interceptors.first.runtimeType.toString(),
          contains('UnifiedAuthInterceptor'));
    });

    test('should throw StateError when accessing dio before initialization',
        () {
      // Act & Assert
      expect(() => dioClient.dio, throwsStateError);
    });

    test('should not initialize multiple times', () async {
      // Act
      await dioClient.initialize();
      await dioClient.initialize(); // Second call should be ignored

      // Assert
      expect(dioClient.dio, isA<Dio>());
    });

    test('should provide getDio convenience method', () async {
      // Arrange
      when(mockAuthService.getValidAccessToken())
          .thenAnswer((_) async => 'test_token');

      // Act
      final dio = await dioClient.getDio();

      // Assert
      expect(dio, isA<Dio>());
      expect(dio.options.headers['Content-Type'], 'application/json');
    });
  });

  group('DioClient Integration Tests', () {
    late DioClient dioClient;
    late MockAuthService mockAuthService;

    setUp(() {
      GetIt.instance.reset();
      mockAuthService = MockAuthService();
      GetIt.instance.registerSingleton<AuthService>(mockAuthService);
      dioClient = DioClient();
    });

    tearDown(() {
      GetIt.instance.reset();
    });

    test('should work with GetIt dependency injection', () async {
      // Arrange
      GetIt.instance.registerSingleton<DioClient>(dioClient);
      when(mockAuthService.getValidAccessToken())
          .thenAnswer((_) async => 'test_token');

      // Act
      final dioFromGetIt = GetIt.instance<DioClient>();
      await dioFromGetIt.initialize();

      // Assert
      expect(dioFromGetIt, same(dioClient));
      expect(dioFromGetIt.dio, isA<Dio>());
    });

    test('should provide configured Dio instance through GetIt', () async {
      // Arrange
      await dioClient.initialize();
      GetIt.instance.registerSingleton<Dio>(dioClient.dio);

      // Act
      final dio = GetIt.instance<Dio>();

      // Assert
      expect(dio, isA<Dio>());
      expect(dio.options.headers['Content-Type'], 'application/json');
    });
  });
}

/// Test helper to verify automatic token injection
class TokenInjectionTestHelper {
  static Future<void> verifyTokenInjection() async {
    // This would be used in integration tests to verify
    // that tokens are automatically injected into requests

    final dio = GetIt.instance<Dio>();

    // Create a test request interceptor to capture headers
    dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        // Verify Authorization header is present
        final authHeader = options.headers['Authorization'];
        expect(authHeader, isNotNull);
        expect(authHeader, startsWith('Bearer '));

        handler.next(options);
      },
    ));
  }
}

/// Mock response helper for testing
class MockResponseHelper {
  static Response<T> createMockResponse<T>({
    required T data,
    int statusCode = 200,
    String statusMessage = 'OK',
  }) {
    return Response<T>(
      data: data,
      statusCode: statusCode,
      statusMessage: statusMessage,
      requestOptions: RequestOptions(path: '/test'),
    );
  }

  static DioException createMockDioException({
    int statusCode = 401,
    String message = 'Unauthorized',
  }) {
    return DioException(
      requestOptions: RequestOptions(path: '/test'),
      response: Response(
        statusCode: statusCode,
        statusMessage: message,
        requestOptions: RequestOptions(path: '/test'),
      ),
      message: message,
    );
  }
}

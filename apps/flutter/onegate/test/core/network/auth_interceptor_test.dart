import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:dio/dio.dart';
import 'package:flutter_onegate/core/network/auth_interceptor.dart';
import 'package:flutter_onegate/services/auth_service/auth_service.dart';

// Generate mocks
@GenerateMocks([AuthService, RequestInterceptorHandler, ErrorInterceptorHandler])
import 'auth_interceptor_test.mocks.dart';

void main() {
  group('UnifiedAuthInterceptor Tests', () {
    late UnifiedAuthInterceptor interceptor;
    late MockAuthService mockAuthService;
    late MockRequestInterceptorHandler mockRequestHandler;
    late MockErrorInterceptorHandler mockErrorHandler;

    setUp(() {
      mockAuthService = MockAuthService();
      mockRequestHandler = MockRequestInterceptorHandler();
      mockErrorHandler = MockErrorInterceptorHandler();
      interceptor = UnifiedAuthInterceptor(authService: mockAuthService);
    });

    group('onRequest', () {
      test('should add Bearer token to request headers', () async {
        // Arrange
        const testToken = 'test_access_token';
        final requestOptions = RequestOptions(path: '/api/gates');
        
        when(mockAuthService.getValidAccessToken())
            .thenAnswer((_) async => testToken);

        // Act
        await interceptor.onRequest(requestOptions, mockRequestHandler);

        // Assert
        expect(requestOptions.headers['Authorization'], 'Bearer $testToken');
        verify(mockRequestHandler.next(requestOptions)).called(1);
      });

      test('should skip auth for excluded endpoints', () async {
        // Arrange
        final requestOptions = RequestOptions(path: '/gatelogin');

        // Act
        await interceptor.onRequest(requestOptions, mockRequestHandler);

        // Assert
        expect(requestOptions.headers['Authorization'], isNull);
        verify(mockRequestHandler.next(requestOptions)).called(1);
        verifyNever(mockAuthService.getValidAccessToken());
      });

      test('should handle null access token gracefully', () async {
        // Arrange
        final requestOptions = RequestOptions(path: '/api/gates');
        
        when(mockAuthService.getValidAccessToken())
            .thenAnswer((_) async => null);

        // Act
        await interceptor.onRequest(requestOptions, mockRequestHandler);

        // Assert
        expect(requestOptions.headers['Authorization'], isNull);
        verify(mockRequestHandler.next(requestOptions)).called(1);
      });

      test('should handle empty access token gracefully', () async {
        // Arrange
        final requestOptions = RequestOptions(path: '/api/gates');
        
        when(mockAuthService.getValidAccessToken())
            .thenAnswer((_) async => '');

        // Act
        await interceptor.onRequest(requestOptions, mockRequestHandler);

        // Assert
        expect(requestOptions.headers['Authorization'], isNull);
        verify(mockRequestHandler.next(requestOptions)).called(1);
      });

      test('should handle auth service errors gracefully', () async {
        // Arrange
        final requestOptions = RequestOptions(path: '/api/gates');
        
        when(mockAuthService.getValidAccessToken())
            .thenThrow(Exception('Auth service error'));

        // Act
        await interceptor.onRequest(requestOptions, mockRequestHandler);

        // Assert
        expect(requestOptions.headers['Authorization'], isNull);
        verify(mockRequestHandler.next(requestOptions)).called(1);
      });
    });

    group('onError', () {
      test('should handle 401 error with successful token refresh', () async {
        // Arrange
        const newToken = 'new_access_token';
        final requestOptions = RequestOptions(path: '/api/gates');
        final dioException = DioException(
          requestOptions: requestOptions,
          response: Response(
            statusCode: 401,
            requestOptions: requestOptions,
          ),
        );

        when(mockAuthService.refreshToken())
            .thenAnswer((_) async => true);
        when(mockAuthService.getValidAccessToken())
            .thenAnswer((_) async => newToken);

        // Act
        await interceptor.onError(dioException, mockErrorHandler);

        // Assert
        verify(mockAuthService.refreshToken()).called(1);
        verify(mockAuthService.getValidAccessToken()).called(1);
        // Note: In a real test, we'd need to mock the Dio retry mechanism
      });

      test('should handle 401 error with failed token refresh', () async {
        // Arrange
        final requestOptions = RequestOptions(path: '/api/gates');
        final dioException = DioException(
          requestOptions: requestOptions,
          response: Response(
            statusCode: 401,
            requestOptions: requestOptions,
          ),
        );

        when(mockAuthService.refreshToken())
            .thenAnswer((_) async => false);

        // Act
        await interceptor.onError(dioException, mockErrorHandler);

        // Assert
        verify(mockAuthService.refreshToken()).called(1);
        verify(mockErrorHandler.next(dioException)).called(1);
      });

      test('should handle non-401 errors normally', () async {
        // Arrange
        final requestOptions = RequestOptions(path: '/api/gates');
        final dioException = DioException(
          requestOptions: requestOptions,
          response: Response(
            statusCode: 500,
            requestOptions: requestOptions,
          ),
        );

        // Act
        await interceptor.onError(dioException, mockErrorHandler);

        // Assert
        verifyNever(mockAuthService.refreshToken());
        verify(mockErrorHandler.next(dioException)).called(1);
      });

      test('should handle 403 authentication error', () async {
        // Arrange
        final requestOptions = RequestOptions(path: '/api/gates');
        final dioException = DioException(
          requestOptions: requestOptions,
          response: Response(
            statusCode: 403,
            requestOptions: requestOptions,
          ),
        );

        // Act
        await interceptor.onError(dioException, mockErrorHandler);

        // Assert
        verify(mockAuthService.logout()).called(1);
        verify(mockErrorHandler.next(dioException)).called(1);
      });

      test('should prevent infinite retry loops', () async {
        // Arrange
        final requestOptions = RequestOptions(
          path: '/api/gates',
          extra: {'retry_count': 1}, // Already retried once
        );
        final dioException = DioException(
          requestOptions: requestOptions,
          response: Response(
            statusCode: 401,
            requestOptions: requestOptions,
          ),
        );

        // Act
        await interceptor.onError(dioException, mockErrorHandler);

        // Assert
        verifyNever(mockAuthService.refreshToken());
        verify(mockErrorHandler.next(dioException)).called(1);
      });
    });

    group('endpoint exclusion', () {
      final excludedEndpoints = [
        '/gatelogin',
        '/auth/login',
        '/register',
        '/forgot-password',
        '/reset-password',
        'gate_base_domain',
        'gate_facial',
      ];

      for (final endpoint in excludedEndpoints) {
        test('should skip auth for $endpoint', () async {
          // Arrange
          final requestOptions = RequestOptions(path: endpoint);

          // Act
          await interceptor.onRequest(requestOptions, mockRequestHandler);

          // Assert
          expect(requestOptions.headers['Authorization'], isNull);
          verifyNever(mockAuthService.getValidAccessToken());
        });
      }
    });
  });

  group('AuthRequestOptions Extension Tests', () {
    test('should enable token debug', () {
      // Arrange
      final options = RequestOptions(path: '/test');

      // Act
      options.enableTokenDebug();

      // Assert
      expect(options.extra['debug_token'], true);
    });

    test('should set and get retry count', () {
      // Arrange
      final options = RequestOptions(path: '/test');

      // Act
      options.retryCount = 2;

      // Assert
      expect(options.retryCount, 2);
    });

    test('should set skip auth', () {
      // Arrange
      final options = RequestOptions(path: '/test');

      // Act
      options.setSkipAuth();

      // Assert
      expect(options.skipAuth, true);
    });

    test('should default retry count to 0', () {
      // Arrange
      final options = RequestOptions(path: '/test');

      // Assert
      expect(options.retryCount, 0);
    });

    test('should default skip auth to false', () {
      // Arrange
      final options = RequestOptions(path: '/test');

      // Assert
      expect(options.skipAuth, false);
    });
  });
}

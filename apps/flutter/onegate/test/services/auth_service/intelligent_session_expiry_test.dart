import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_onegate/services/auth_service/token_validation_service.dart';
import 'package:flutter_onegate/services/auth_service/refresh_token_error_handler.dart';
import 'package:flutter_onegate/services/auth_service/riverpod/auth_tokens.dart';

void main() {
  group('Intelligent Session Expiry Tests', () {
    group('AuthTokens Expiration Logic', () {
      test('should identify both tokens as valid', () {
        // Arrange
        final now = DateTime.now();
        final tokens = AuthTokens(
          access: 'valid_access_token',
          refresh: 'valid_refresh_token',
          accessExpiresAt: now.add(const Duration(hours: 1)), // Valid for 1 hour
          refreshExpiresAt: now.add(const Duration(days: 1)), // Valid for 1 day
        );

        // Act & Assert
        expect(tokens.isValid, isTrue);
        expect(tokens.isAccessExpired, isFalse);
        expect(tokens.isRefreshExpired, isFalse);
      });

      test('should identify access token expired but refresh valid', () {
        // Arrange
        final now = DateTime.now();
        final tokens = AuthTokens(
          access: 'expired_access_token',
          refresh: 'valid_refresh_token',
          accessExpiresAt: now.subtract(const Duration(minutes: 1)), // Expired 1 min ago
          refreshExpiresAt: now.add(const Duration(days: 1)), // Valid for 1 day
        );

        // Act & Assert
        expect(tokens.isAccessExpired, isTrue);
        expect(tokens.isRefreshExpired, isFalse);
        expect(tokens.isValid, isFalse); // Invalid because access is expired
      });

      test('should identify both tokens as expired', () {
        // Arrange
        final now = DateTime.now();
        final tokens = AuthTokens(
          access: 'expired_access_token',
          refresh: 'expired_refresh_token',
          accessExpiresAt: now.subtract(const Duration(minutes: 1)), // Expired 1 min ago
          refreshExpiresAt: now.subtract(const Duration(minutes: 1)), // Expired 1 min ago
        );

        // Act & Assert
        expect(tokens.isAccessExpired, isTrue);
        expect(tokens.isRefreshExpired, isTrue);
        expect(tokens.isValid, isFalse);
      });
    });

    group('Session Expired Modal Decision Logic', () {
      test('should NOT show modal for network timeout errors', () async {
        // Act
        final shouldShow = await TokenValidationService.shouldShowSessionExpiredModal(
          RefreshTokenFailureType.networkTimeout,
          null,
        );

        // Assert
        expect(shouldShow, isFalse);
      });

      test('should NOT show modal for server errors', () async {
        // Act
        final shouldShow = await TokenValidationService.shouldShowSessionExpiredModal(
          RefreshTokenFailureType.serverError,
          null,
        );

        // Assert
        expect(shouldShow, isFalse);
      });

      test('should NOT show modal for connection errors', () async {
        // Act
        final shouldShow = await TokenValidationService.shouldShowSessionExpiredModal(
          RefreshTokenFailureType.connectionError,
          null,
        );

        // Assert
        expect(shouldShow, isFalse);
      });

      test('should NOT show modal for storage errors', () async {
        // Act
        final shouldShow = await TokenValidationService.shouldShowSessionExpiredModal(
          RefreshTokenFailureType.storageError,
          null,
        );

        // Assert
        expect(shouldShow, isFalse);
      });

      test('should check actual token state for authentication failures', () async {
        // Act
        final shouldShow = await TokenValidationService.shouldShowSessionExpiredModal(
          RefreshTokenFailureType.refreshTokenExpired,
          null,
        );

        // Assert - This will depend on actual token state in storage
        expect(shouldShow, isA<bool>());
      });
    });

    group('Token Debug Information', () {
      test('should return debug information without errors', () async {
        // Act
        final debugInfo = await TokenValidationService.getTokenDebugInfo();

        // Assert
        expect(debugInfo, isA<Map<String, dynamic>>());
        expect(debugInfo.containsKey('timestamp'), isTrue);
        expect(debugInfo['timestamp'], isA<String>());
      });
    });

    group('Integration Scenarios', () {
      test('should handle scenario: access expired, refresh valid, network error', () async {
        // This simulates the scenario where:
        // 1. Access token is expired
        // 2. Refresh token is still valid
        // 3. Network error occurs during refresh attempt
        
        // In this case, the modal should NOT be shown because:
        // - It's a temporary network error
        // - The refresh token is still valid, so authentication can be recovered
        
        final shouldShow = await TokenValidationService.shouldShowSessionExpiredModal(
          RefreshTokenFailureType.networkTimeout,
          null,
        );

        expect(shouldShow, isFalse);
      });

      test('should handle scenario: both tokens expired, authentication failure', () async {
        // This simulates the scenario where:
        // 1. Both access and refresh tokens are expired
        // 2. Authentication failure occurs
        
        // In this case, the modal SHOULD be shown because:
        // - It's a permanent authentication failure
        // - Both tokens are expired, so automatic recovery is impossible
        
        final shouldShow = await TokenValidationService.shouldShowSessionExpiredModal(
          RefreshTokenFailureType.refreshTokenExpired,
          null,
        );

        // Note: This will check actual token state, so result may vary
        expect(shouldShow, isA<bool>());
      });
    });

    group('Token Expiration Buffer Logic', () {
      test('should detect tokens expiring within buffer time', () {
        // Arrange
        final now = DateTime.now();
        final tokens = AuthTokens(
          access: 'access',
          refresh: 'refresh',
          accessExpiresAt: now.add(const Duration(minutes: 1)), // Expires in 1 min
          refreshExpiresAt: now.add(const Duration(minutes: 3)), // Expires in 3 min
        );

        // Act & Assert
        expect(tokens.willAccessExpireWithin(const Duration(minutes: 2)), isTrue);
        expect(tokens.willAccessExpireWithin(const Duration(seconds: 30)), isFalse);
        expect(tokens.willRefreshExpireWithin(const Duration(minutes: 5)), isTrue);
        expect(tokens.willRefreshExpireWithin(const Duration(minutes: 2)), isFalse);
      });

      test('should calculate time until expiration correctly', () {
        // Arrange
        final now = DateTime.now();
        final tokens = AuthTokens(
          access: 'access',
          refresh: 'refresh',
          accessExpiresAt: now.add(const Duration(minutes: 5)),
          refreshExpiresAt: now.add(const Duration(hours: 2)),
        );

        // Act
        final accessTimeLeft = tokens.timeUntilAccessExpiration;
        final refreshTimeLeft = tokens.timeUntilRefreshExpiration;

        // Assert
        expect(accessTimeLeft, isNotNull);
        expect(refreshTimeLeft, isNotNull);
        expect(accessTimeLeft!.inMinutes, closeTo(5, 1));
        expect(refreshTimeLeft!.inHours, closeTo(2, 1));
      });
    });

    group('Requirements Validation', () {
      test('should NOT show modal when only access token is expired', () {
        // This validates the requirement:
        // "When only the access token is expired (but refresh token is still valid) 
        // - the system should silently refresh the access token"
        
        final now = DateTime.now();
        final tokens = AuthTokens(
          access: 'expired_access',
          refresh: 'valid_refresh',
          accessExpiresAt: now.subtract(const Duration(minutes: 1)), // Expired
          refreshExpiresAt: now.add(const Duration(hours: 1)), // Valid
        );

        // Access expired but refresh valid = should NOT show modal
        expect(tokens.isAccessExpired, isTrue);
        expect(tokens.isRefreshExpired, isFalse);
      });

      test('should show modal only when both tokens are expired', () {
        // This validates the requirement:
        // "The session expired bottom sheet should ONLY be displayed when:
        // - Both refresh token AND access token are expired/invalid"
        
        final now = DateTime.now();
        final tokens = AuthTokens(
          access: 'expired_access',
          refresh: 'expired_refresh',
          accessExpiresAt: now.subtract(const Duration(minutes: 1)), // Expired
          refreshExpiresAt: now.subtract(const Duration(minutes: 1)), // Expired
        );

        // Both tokens expired = should show modal
        expect(tokens.isAccessExpired, isTrue);
        expect(tokens.isRefreshExpired, isTrue);
        expect(tokens.isValid, isFalse);
      });
    });
  });
}
